@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }

  /* Dark theme (default) */
  html.dark body {
    @apply bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 font-sans antialiased text-white;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }

  /* Light theme */
  html.light body {
    @apply bg-gradient-to-br from-slate-50 to-blue-50 font-sans antialiased text-gray-900;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }

  /* Fallback for no theme class */
  body {
    @apply bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 font-sans antialiased text-white;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Glass morphism utilities - Dark theme */
  html.dark .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  html.dark .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-white/10;
  }

  /* Glass morphism utilities - Light theme */
  html.light .glass {
    @apply bg-black/5 backdrop-blur-md border border-gray-200/50;
  }

  html.light .glass-dark {
    @apply bg-white/80 backdrop-blur-md border border-gray-200;
  }

  /* Fallback glass styles */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-white/10;
  }

  /* Modern button styles */
  .btn-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200;
  }

  .btn-glass {
    @apply glass hover:bg-white/20 text-white font-semibold py-3 px-6 rounded-xl shadow-glass hover:shadow-xl transform hover:scale-105 transition-all duration-200;
  }

  /* Modern card styles */
  .card-modern {
    @apply bg-white/80 backdrop-blur-sm border border-white/20 rounded-2xl shadow-glass hover:shadow-xl transition-all duration-300;
  }

  .card-glass {
    @apply glass rounded-2xl shadow-glass hover:shadow-xl transition-all duration-300;
  }

  /* Input styles */
  .input-modern {
    @apply w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }

  /* Gradient text */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  .text-gradient-accent {
    @apply bg-gradient-to-r from-accent-500 to-secondary-500 bg-clip-text text-transparent;
  }

  /* Animated backgrounds */
  .bg-animated {
    @apply bg-gradient-to-br from-primary-400 via-secondary-500 to-accent-400 animate-gradient-xy;
    background-size: 400% 400%;
  }

  .bg-mesh {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    background-size: 400% 400%;
    animation: gradient-xy 15s ease infinite;
  }
}

@layer utilities {
  /* Custom scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Smooth transitions */
  .transition-smooth {
    @apply transition-all duration-300 ease-in-out;
  }

  /* Hover lift effect */
  .hover-lift {
    @apply transform hover:scale-105 hover:-translate-y-1 transition-all duration-200;
  }

  /* Glow effects */
  .glow-primary {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .glow-secondary {
    box-shadow: 0 0 20px rgba(217, 70, 239, 0.3);
  }

  /* Text shadows */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Calendar specific styles */
  .calendar-day {
    @apply relative p-2 min-h-[60px] cursor-pointer rounded-lg transition-all duration-200;
  }

  .calendar-day:hover {
    @apply bg-white/10;
  }

  .calendar-day.today {
    @apply bg-primary-500/20 border border-primary-500/50;
  }

  .calendar-day.selected {
    @apply bg-secondary-500/20 border border-secondary-500/50;
  }

  .calendar-day.other-month {
    @apply text-white/40;
  }

  /* Task priority colors */
  .priority-urgent {
    @apply text-red-400 bg-red-500/20;
  }

  .priority-high {
    @apply text-orange-400 bg-orange-500/20;
  }

  .priority-medium {
    @apply text-yellow-400 bg-yellow-500/20;
  }

  .priority-low {
    @apply text-green-400 bg-green-500/20;
  }

  /* Task status colors */
  .status-completed {
    @apply text-green-400 bg-green-500/20;
  }

  .status-in-progress {
    @apply text-blue-400 bg-blue-500/20;
  }

  .status-pending {
    @apply text-gray-400 bg-gray-500/20;
  }

  .status-revise {
    @apply text-purple-400 bg-purple-500/20;
  }
}